.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 170vh;
  background-image: url(../../../public/assets/pagewormu.jpg);
  background-size: cover;
  background-position: center;
  overflow-x: hidden;
}

.pageContent {
  flex: 1;
  padding: 20px;
  padding-top: 40px; /* Reduced since navbar is now sticky */
  color: white;
 
}

.title {
  font-size: 24px;
  font-weight: bold;
  font-family: Quicksand;
  margin: 30px 0 60px 30px; /* Increased bottom margin from 40px to 60px */
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  row-gap: 100px;
  column-gap: 90px;
  padding: 0 30px;
  margin-top: 80px; /* Adjust this value to move the grid lower */
}

/* Muscle group card: text + image block */
.muscleCard {
  text-align: center;
  margin-top: -50px;
  transition: all 0.9s ease;
  opacity: 1;
  transform: scale(1);
}

/* Hover effect without blur - only affects the hovered card */
.muscleCard:hover {
  opacity: 1;
  transform: scale(0.95);
  z-index: 1;
  cursor: pointer;

  /* Add orange glow border effect */
  box-shadow: 0 0 15px 3px orange;
  border-radius: 10px;
  background-color: rgba(255, 165, 0, 0.1); /* subtle orange tint */
}

/* Text styling */
.muscleCard p {
  color: orange;
  font-family: Quicksand;
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

/* Text glow on hover */
.muscleCard:hover p {
  text-shadow: 0 0 8px orange;
}
